package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zsmall.system.entity.domain.BlogArticleEntity;
import com.zsmall.system.entity.domain.dto.BlogQueryDTO;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【blog_article(博客文章表)】的数据库操作Mapper
* @createDate 2022-01-08 17:08:05
*/
public interface BlogArticleMapper extends BaseMapper<BlogArticleEntity> {

  IPage<BlogArticleEntity> getBlogPage(Page<BlogArticleEntity> page, @Param("query") BlogQueryDTO query);
}




