package com.zsmall.system.entity.domain;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 渠道仓库关联信息对象 channel_warehouse_info
 *
 * <AUTHOR> Li
 * @date 2024-11-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("channel_warehouse_info")
public class ChannelWarehouseInfo extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 租户渠道表id
     */
    private Long tenantSaleChannelId;

    /**
     * 管理仓库id
     */
    private Long warehouseAdminId;

    /**
     * 仓库code
     */
    private String warehouseCode;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 店铺仓库名称
     */
    private String channelWarehouseName;

    /**
     * 店铺仓库标识符
     */
    private String channelWarehouseCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
