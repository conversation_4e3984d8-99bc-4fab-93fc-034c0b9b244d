package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 供应商入驻基本信息表
 * @TableName tenant_sup_settle_in_basic
 */
@TableName(value ="tenant_sup_settle_in_basic")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TenantSupSettleInBasic extends NoDeptTenantEntity {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 省/州id
     */
    private Long stateId;

    /**
     * 省/州文本
     */
    private String stateText;

    /**
     * 城市文本
     */
    private String cityText;

    /**
     * 注册地址
     */
    private String registeredAddress;

    /**
     * 法人归属地
     */
    private String legalPersonPlace;

    /**
     * 证件类型
     */
    private Integer documentType;

    /**
     * 证件号
     */
    private String documentNumber;

    /**
     * 法定代表人姓名
     */
    private String legalPersonName;

    /**
     * 公司税务等级号
     */
    private String companyTaxRegistrationNumber;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public TenantSupSettleInBasic(Long id, String companyName, String socialCreditCode, Long countryId, Long stateId, String stateText, String cityText, String registeredAddress, String legalPersonPlace, Integer documentType, String documentNumber, String legalPersonName) {
        this.id = id;
        this.companyName = companyName;
        this.socialCreditCode = socialCreditCode;
        this.countryId = countryId;
        this.stateId = stateId;
        this.stateText = stateText;
        this.cityText = cityText;
        this.registeredAddress = registeredAddress;
        this.legalPersonPlace = legalPersonPlace;
        this.documentType = documentType;
        this.documentNumber = documentNumber;
        this.legalPersonName = legalPersonName;
    }


}
