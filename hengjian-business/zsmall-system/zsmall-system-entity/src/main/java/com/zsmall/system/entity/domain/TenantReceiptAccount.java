package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.transaction.ReceiptAccountTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;



/**
 * 收款账户对象 tenant_receipt_account
 *
 * <AUTHOR> Li
 * @date 2023-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenant_receipt_account")
public class TenantReceiptAccount extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 收款账户code
     */
    private String accountCode;

    /**
     * 账户状态
     */
    private Integer accountStatus;

    /**
     * 账户类型
     */
    private ReceiptAccountTypeEnum accountType;

    /**
     * 停用时间
     */
    private LocalDateTime unavailabilityTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
