package com.zsmall.system.entity.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 分销商账单明细导出
 */
@Data
public class TenantBillDetailsExportDto {
    @ExcelProperty("账单编号")
    private String billNo;

    @ExcelProperty("分销商ID")
    private String tenantId;

    @ExcelProperty("供应商ID")
    private String supperTenantId;

    @ExcelProperty("订单号")
    private String orderNo;

    @ExcelProperty("渠道订单号")
    private String channelOrderNo;

    @ExcelProperty("交易类型")
    private String orderStatus;

    @ExcelProperty("支付方式")
    private String orderPaymentMethods;

    @ExcelProperty("下单时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private String orderExportTime;

    @ExcelProperty("发货方式")
    private String supportedLogistics;

    @ExcelProperty("SKU ID")
    private String productSkuCode;

    @ExcelProperty("币种")
    private String currencyCode;

    @ExcelProperty("产品小计")
    private BigDecimal productSkuPrice;

    @ExcelProperty("单价")
    private BigDecimal unitPrice;
    @ExcelProperty("操作费")
    private BigDecimal operationFee;

    @ExcelProperty("尾程派送费")
    private BigDecimal finalDeliveryFee;
    @ExcelProperty("数量")
    private Integer productQuantity;

    @ExcelProperty("订单总金额")
    private BigDecimal orderAmount;
    /**
     * 订单金额
     */
    @ExcelIgnore
    private BigDecimal orderTotalAmount;

    /**
     * 订单退款金额
     */
    @ExcelIgnore
    private BigDecimal orderRefundTotalAmount;
    @ExcelIgnore
    private Date orderTime;

}
