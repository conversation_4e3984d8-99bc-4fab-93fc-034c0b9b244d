package com.zsmall.system.entity.mapper;


import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.TransactionsOrders;
import com.zsmall.system.entity.domain.vo.transaction.TransactionsOrdersVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 交易记录-订单关联Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
public interface TransactionsOrdersMapper extends BaseMapperPlus<TransactionsOrders, TransactionsOrdersVo> {
    /**
     * 根据交易记录查询关联的店铺名称
     * @param transactionNoSet
     * @return list
     */
    List<Map<String, String>> getChannelName(@Param("transactionNoSet") Set<Long> transactionNoSet);
}
