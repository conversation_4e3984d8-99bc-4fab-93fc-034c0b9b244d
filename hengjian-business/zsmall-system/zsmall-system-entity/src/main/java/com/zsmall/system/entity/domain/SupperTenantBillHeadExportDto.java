package com.zsmall.system.entity.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *  供应商账单头导出
 */
@Data
public class SupperTenantBillHeadExportDto {
    @ExcelProperty("供应商")
    private String tenantId;
    @ExcelProperty("账单编号")
    private String billNo;
    @ExcelProperty("结算周期")
    private String billTime;
    @ExcelIgnore
    private Date billStartTime;
    @ExcelIgnore
    private Date billEndTime;
    @ExcelProperty("本期收入")
    private BigDecimal totalShippedAmount;
    @ExcelProperty("本期指出")
    private BigDecimal totalRefundAmount;
    @ExcelProperty("本期循环保证金")
    private BigDecimal currentRevolvingOrderAmount;
    @ExcelProperty("上期循环保证金")
    private BigDecimal previousRevolvingOrderAmount;
    @ExcelProperty("本期总金额")
    private BigDecimal totalAmount;
    @ExcelProperty("账单状态")
    private String settlementState;
}
