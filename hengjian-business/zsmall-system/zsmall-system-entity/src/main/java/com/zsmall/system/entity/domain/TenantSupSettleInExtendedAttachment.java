package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 供应商入驻扩展信息附件表
 * @TableName tenant_sup_settle_in_extended_attachment
 */
@TableName(value ="tenant_sup_settle_in_extended_attachment")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TenantSupSettleInExtendedAttachment extends SortEntity {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 存储对象主键
     */
    private Long ossId;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件原名
     */
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    private String attachmentSuffix;

    /**
     * 附件存放路径
     */
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    private Integer attachmentSort;

    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 供应商入驻扩展信息id
     */
    private Long extendedId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
