package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.TenantReceiptAccountPayoneer;
import com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountPayoneerVo;
import org.apache.ibatis.annotations.Param;

/**
 * 收款账户Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-07-04
 */
public interface TenantReceiptAccountPayoneerMapper extends BaseMapperPlus<TenantReceiptAccountPayoneer, TenantReceiptAccountPayoneerVo> {


    /**
     * 查询Payoneer
     * @param queryWrapper
     * @return
     */
    TenantReceiptAccountPayoneerVo selectReceiptAccountPayoneer(@Param(Constants.WRAPPER) Wrapper<TenantReceiptAccountPayoneer> queryWrapper);


}
