package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.TenantReceiptAccount;
import com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 收款账户Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-07-04
 */
public interface TenantReceiptAccountMapper extends BaseMapperPlus<TenantReceiptAccount, TenantReceiptAccountVo> {

    /**
     * 查询是否存在相同账号
     * @param accountType
     * @param params
     * @return
     */
    Integer existReceiptAccount(@Param("accountType") String accountType, @Param("params") Map<String, Object> params);

    /**
     * 查询列表
     * @param queryWrapper
     * @return
     */
    List<TenantReceiptAccount> selectReceiptAccounts(@Param(Constants.WRAPPER) Wrapper<TenantReceiptAccount> queryWrapper);


    /**
     * 查询账户
     * @param queryWrapper
     * @return
     */
    TenantReceiptAccount selectReceiptAccount(@Param(Constants.WRAPPER) Wrapper<TenantReceiptAccount> queryWrapper);
}
