package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 供应商入驻公司联系人信息表
 * @TableName tenant_sup_settle_in_contact
 */
@TableName(value ="tenant_sup_settle_in_contact")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TenantSupSettleInContact extends SortEntity {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 姓
     */
    private String firstName;

    /**
     * 名
     */
    private String lastName;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号码国际区号
     */
    private String areaCode;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * email
     */
    private String email;

    /**
     * 通讯软件类型
     */
    private String msgAppType;

    /**
     * 通讯软件账号
     */
    private String msgAppAccount;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 省/州id
     */
    private Long stateId;

    /**
     * 省/州文本
     */
    private String stateText;

    /**
     * 城市文本
     */
    private String cityText;

    /**
     * 具体地址
     */
    private String address;

    /**
     * 联系人类型
     */
    private String contactType;

    /**
     * 供应商入驻基础信息id
     */
    private Long basicId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
