package com.zsmall.system.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.WorldLocation;
import com.zsmall.system.entity.domain.bo.worldLocation.WorldLocationBo;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationSelectVo;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 世界地点Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
public interface WorldLocationMapper extends BaseMapperPlus<WorldLocation, WorldLocationVo> {

    /**
     * 查询世界地点列表（提供给下拉选使用）
     * @param bo
     * @return
     */
    List<WorldLocationSelectVo> queryListForSelect(@Param("bo") WorldLocationBo bo);

    /**
     * 根据国家代号查询所有州/省编号
     * @param countryCode
     * @return
     */
    List<String> queryChildCodeList(@Param("countryCode") String countryCode);

    WorldLocation getByLocationOthName(@Param("state")String state);
}
