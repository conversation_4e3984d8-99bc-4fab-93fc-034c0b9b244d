package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.TenantSupSettleInReviewRecord;
import com.zsmall.system.entity.domain.dto.settleInBasic.UserSupReviewRecordDto;
import com.zsmall.system.entity.domain.dto.settleInBasic.UserSupReviewRecordParamDto;
import com.zsmall.system.entity.domain.vo.settleInBasic.TenantSupSettleInReviewRecordVo;
import org.apache.ibatis.annotations.Param;

/**
 * 供应商入驻信息审核记录Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-05-30
 */
public interface TenantSupSettleInReviewRecordMapper extends BaseMapperPlus<TenantSupSettleInReviewRecord, TenantSupSettleInReviewRecordVo> {


    IPage<UserSupReviewRecordDto> getListPage(Page<UserSupReviewRecordDto> page, @Param("dto") UserSupReviewRecordParamDto dto);


}
