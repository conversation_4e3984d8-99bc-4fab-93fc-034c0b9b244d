package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 交易记录-批发订单关联表
 * @TableName transactions_wholesale_intention_order
 */
@TableName(value ="transactions_wholesale_intention_order")
@Data
public class TransactionsWholesaleIntentionOrder implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 交易记录主键
     */
    private Long transactionsId;

    /**
     * 国外现货批发意向订单主键
     */
    private Long wholesaleIntentionOrderId;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
