package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zsmall.system.entity.domain.UserFeedback;
import com.zsmall.system.entity.domain.bo.userFeedback.UserFeedbackPageBo;
import org.apache.ibatis.annotations.Param;

/**
 * 用户反馈-Mapper层
 * <AUTHOR>
 * @date 2023/9/11
 */
public interface UserFeedbackMapper extends BaseMapper<UserFeedback> {

    IPage<UserFeedback> queryPage(@Param("bo") UserFeedbackPageBo bo, Page<UserFeedback> page);

}
