package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商城活动详情表
 * <AUTHOR>
 * @date 2023/9/6
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "marketplace_activity_config_item", autoResultMap = true)
public class MarketplaceActivityConfigItem extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 商城活动编号
     */
    @TableField(value = "mp_activity_code")
    private String mpActivityCode;

    /**
     * 商城活动详情Key
     */
    @TableField(value = "mp_activity_key")
    private String mpActivityKey;

    /**
     * 商城活动详情Value
     */
    @TableField(value = "mp_activity_value")
    private String mpActivityValue;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
