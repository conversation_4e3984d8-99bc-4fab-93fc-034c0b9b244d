package com.zsmall.system.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.ConfZip;
import com.zsmall.system.entity.domain.WorldLocation;
import com.zsmall.system.entity.domain.vo.confZip.ConfZipVo;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/12 16:29
 */
public interface ConfZipMapper extends BaseMapperPlus<ConfZip, ConfZipVo> {
    List<ConfZip> selectConfZipByStateAndCounty(@Param("stateName")String stateName, @Param("country") String country);
}
