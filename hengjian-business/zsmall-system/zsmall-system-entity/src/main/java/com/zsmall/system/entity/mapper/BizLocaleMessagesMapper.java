package com.zsmall.system.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.hengjian.common.tenant.mapper.TenantMapperPlus;
import com.zsmall.system.entity.domain.BizLocaleMessages;
import com.zsmall.system.entity.domain.vo.BizLocaleMessagesVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 翻译消息数据Mapper接口
 *
 * <AUTHOR> Li
 * @date 2024-08-06
 */
public interface BizLocaleMessagesMapper extends TenantMapperPlus<BizLocaleMessages, BizLocaleMessagesVo> {

    /**
     * 软删数据
     *
     * @param ids
     * @param delTime
     * @param delFlag
     * @param updateBy
     * @param updateTime
     */
    void updateDelById(@Param("ids") List<Long> ids, @Param("delTime")Date delTime,@Param("delFlag")String delFlag,@Param("updateBy")Long updateBy,@Param("updateTime")Date updateTime);

    /**
     * 根据语言键删除数据
     *
     * @param langKeyList
     */
    void deleteByLangKeyList(@Param("langKeyList") List<String> langKeyList);
}
