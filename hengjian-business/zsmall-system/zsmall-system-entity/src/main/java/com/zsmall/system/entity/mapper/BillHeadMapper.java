package com.zsmall.system.entity.mapper;


import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.BillHead;
import com.zsmall.system.entity.domain.vo.billHead.BillHeadVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账单-newMapper接口
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
public interface BillHeadMapper extends BaseMapperPlus<BillHead, BillHeadVo> {

    BigDecimal sumTotalAmount(@Param("tenantId") String tenantId);

    BigDecimal sumCurrentRevolvingOrderAmount(@Param("tenantId") String tenantId);

    void batchUpdateBillStatus(Integer billStatus, List<Long> billId);
}
