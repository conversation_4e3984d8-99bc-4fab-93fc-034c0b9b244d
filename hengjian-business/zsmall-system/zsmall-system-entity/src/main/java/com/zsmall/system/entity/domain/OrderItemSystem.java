package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.orderItem.ShippingOrderStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/4/22 17:23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("order_item")
public class OrderItemSystem extends NoDeptTenantEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 供货商租户编号 此属性的值是跟着商品上的
     */
    private String supplierTenantId;

    /**
     * 主订单表主键
     */
    private Long orderId;

    /**
     * 主订单编号
     */
    private String orderNo;

    /**
     * 子订单编号
     */
    private String orderItemNo;

    /**
     * Sku唯一编号（ItemNo.）
     */
    private String productSkuCode;

    /**
     * 子订单商品总数量
     */
    private Integer totalQuantity;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelType;

    /**
     * 订单状态
     */
    private OrderStateType orderState = OrderStateType.UnPaid;

    /**
     * 订单商品库存管理方
     */
    private StockManagerEnum stockManager;

    /**
     * 子订单履约进度（未发货、已发货、已履约等）
     */
    private LogisticsProgress fulfillmentProgress = LogisticsProgress.UnDispatched;

    /**
     * 物流类型：PickUp-自提，DropShipping-代发
     */
    private LogisticsTypeEnum logisticsType;

    /**
     * 发货时间
     */
    private Date dispatchedTime;

    /**
     * 履约时间
     */
    private Date fulfillmentTime;

    /**
     * 参与活动类型（为空代表未参与活动）
     */
    private ProductActivityTypeEnum activityType;

    /**
     * 参与的子活动编号（为空代表未参与）
     */
    private String activityCode;

    /**
     * 发货单状态（用于标记第三方仓库的发货单创建状态）
     */
    private ShippingOrderStateEnum shippingOrderState;

    /**
     * 已归还的商品数量（退款等操作，需要归还库存）
     */
    private Integer restockQuantity = 0;

//    金额-------------------------------------

    /**
     * 供货商应得收入
     */
    private BigDecimal supplierIncomeEarned;

    /**
     * 原始应付单价（供货商）
     */
    private BigDecimal originalPayableUnitPrice;

    /**
     * 原始应付总金额（供货商）
     */
    private BigDecimal originalPayableTotalAmount;

    /**
     * 原始已预付单价（供货商）
     */
    private BigDecimal originalPrepaidUnitPrice;

    /**
     * 原始已预付总金额（供货商）
     */
    private BigDecimal originalPrepaidTotalAmount;

    /**
     * 原始实际支付单价（供货商）
     */
    private BigDecimal originalActualUnitPrice;

    /**
     * 原始实际支付总金额（供应商）
     */
    private BigDecimal originalActualTotalAmount;

    /**
     * 原始售后可执行金额（供货商）
     */
    private BigDecimal originalRefundExecutableAmount;

    /**
     * 平台应付单价（平台、分销商）
     */
    private BigDecimal platformPayableUnitPrice;

    /**
     * 平台应付总金额（平台、分销商）
     */
    private BigDecimal platformPayableTotalAmount;

    /**
     * 平台已预付单价（平台、分销商）
     */
    private BigDecimal platformPrepaidUnitPrice;

    /**
     * 平台已预付总金额（平台、分销商）
     */
    private BigDecimal platformPrepaidTotalAmount;

    /**
     * 平台实际支付单价（平台、分销商）
     */
    private BigDecimal platformActualUnitPrice;

    /**
     * 平台实际支付总金额（平台、分销商）
     */
    private BigDecimal platformActualTotalAmount;

    /**
     * 平台售后可执行金额（平台、分销商）
     */
    private BigDecimal platformRefundExecutableAmount;

    /**
     * 渠道销售单价（分销商铺货时Mark Up之后的价格）
     */
    private BigDecimal channelSaleUnitPrice;

    /**
     * 渠道销售总金额（分销商铺货时Mark Up之后的价格乘以数量）
     */
    private BigDecimal channelSaleTotalAmount;

    /**
     * 子订单价格
     */
    @TableField(exist = false)
    private OrderItemPriceSystem orderItemPrice;



//    金额-------------------------------------

    /**
     * 渠道店铺主键
     */
    private Long channelId;

    /**
     * 第三方渠道子订单编号（如果有则记录）
     */
    private String channelItemNo;


    /**
     * 渠道单号
     */
    private String channelOrderNo;

    /**
     * 渠道SKU
     */
    private String channelSku;

    private Long siteId;
    private String countryCode;
    private String currencySymbol;
    private String currency;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    /**
     * 子订单商品信息
     */
    @TableField(exist = false)
    private OrderItemProductSkuSystem orderItemProductSku;
}
