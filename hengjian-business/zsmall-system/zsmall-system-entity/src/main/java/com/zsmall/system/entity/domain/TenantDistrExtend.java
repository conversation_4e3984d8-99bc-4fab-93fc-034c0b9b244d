package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 租户分销商-拓展信息对象 tenant_distr_extend
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenant_distr_extend")
public class TenantDistrExtend extends TenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 是否存在公司主体（0-无，1-有）
     */
    private Boolean hasCompany;

    /**
     * 主营类目（一级分类id，用逗号分隔）
     */
    private String mainCategories;

    /**
     * 团队规模
     */
    private String teamSize;

    /**
     * 公司所在国家id
     */
    private Long companyCountryId;

    /**
     * 公司所在国家（非中美手动填写）
     */
    private String companyCountryText;

    /**
     * 公司所在州/省id
     */
    private Long companyStateId;

    /**
     * 公司所在州/省（非中美手动填写）
     */
    private String companyStateText;

    /**
     * 公司所在市id
     */
//    private Long companyCityId;

    /**
     * 公司所在市（非中美手动填写）
     */
    private String companyCityText;

    /**
     * 公司联系地址
     */
    private String companyContactAddress;

    /**
     * 近一年年销售规模（万）
     */
    private String recentAnnualSalesScale;

    /**
     * 跨境电商经验
     */
    private String cecExperience;

    /**
     * 其他线上销售渠道经验（逗号隔开）
     */
    private String otherExperience;

    /**
     * 其他渠道经验
     */
    private String otherChannelExperience;

    /**
     * 公司税务等级号
     */
    private String companyTaxRegistrationNumber;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
