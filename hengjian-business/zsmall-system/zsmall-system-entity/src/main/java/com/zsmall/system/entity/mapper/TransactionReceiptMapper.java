package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.common.enums.transaction.ReceiptReviewStateEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.system.entity.domain.TransactionReceipt;
import com.zsmall.system.entity.domain.vo.payment.ReceiptTotalAmountVo;
import com.zsmall.system.entity.domain.vo.transaction.RechargeReceiptStatsVo;
import com.zsmall.system.entity.domain.vo.transaction.TransactionReceiptVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 交易回执单Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-06-16
 */
public interface TransactionReceiptMapper extends BaseMapperPlus<TransactionReceipt, TransactionReceiptVo> {

    /**
     * 充值统计
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    RechargeReceiptStatsVo getRechargeStatistics(@Param("tenantId") String tenantId,@Param("currency")String currency);

    /**
     * 统计平台充值总金额
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    ReceiptTotalAmountVo statsPlatformTotalAmount(@Param("transactionType") String transactionType);

    /**
     * 根据审核状态和交易类型汇总交易金额
     * @param transactionType
     * @param states
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    BigDecimal sumAmountByTransactionTypeAndReviewStatus(@Param("transactionType") TransactionTypeEnum transactionType,
                                                         @Param("states") List<ReceiptReviewStateEnum> states);

    /**
     * 根据交易记录Id查询交易回执单
     * @param transactionId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    TransactionReceipt selectByTransactionId(@Param("transactionId") Long transactionId);

    String getThirdChannelFlag(@Param("tenantId")String tenantId);

    /**
     * 根据店铺标识查询租户Id
     * @param thirdChannelFlag
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> getTenantIdByThirdChannelFlag(@Param("thirdChannelFlag")String thirdChannelFlag);
}
