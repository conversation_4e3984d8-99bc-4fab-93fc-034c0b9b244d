package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.system.entity.domain.TenantWallet;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 租户钱包表-mapper接口
 *
 * <AUTHOR>
 * @date 2023/6/9
 */
public interface TenantWalletMapper extends BaseMapper<TenantWallet> {

    /**
     * 统计分销商余额
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    BigDecimal sumDistributorBalance();

    /**
     * 根据币种统计分销商余额
     *
     * @param currency
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    BigDecimal sumDistributorBalanceByCurrency(@Param("currency") String currency);
}
