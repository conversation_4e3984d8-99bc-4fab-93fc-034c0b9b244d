package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商城活动表
 * <AUTHOR>
 * @date 2023/9/6
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "marketplace_activity_config")
public class MarketplaceActivityConfig extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 商城活动编号
     */
    @TableField(value = "mp_activity_code")
    private String mpActivityCode;

    /**
     * 商城活动名称
     */
    @TableField(value = "mp_activity_name")
    private String mpActivityName;

    /**
     * 商城活动主题图
     */
    @TableField(value = "mp_activity_img")
    private String mpActivityImg;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
