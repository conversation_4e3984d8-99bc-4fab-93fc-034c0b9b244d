package com.zsmall.system.entity.domain;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 渠道扩展-Rakuten品类表
 * <AUTHOR>
 * @date 2023/10/26
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "channel_extend_rakuten_genre", autoResultMap = true)
public class ChannelExtendRakutenGenre extends NoDeptBaseEntity {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 父级品类ID（0为根节点）
     */
    @TableField(value = "parent_genre_id")
    private String parentGenreId;

    /**
     * 品类ID
     */
    @TableField(value = "genre_id")
    private String genreId;

    /**
     * 品类ID关系路线
     */
    @TableField(value = "genre_id_path", typeHandler = JacksonTypeHandler.class)
    private JSONArray genreIdPath;

    /**
     * 日文名称
     */
    @TableField(value = "name_ja")
    private String nameJa;

    /**
     * 日文名称关系路线
     */
    @TableField(value = "name_ja_path", typeHandler = JacksonTypeHandler.class)
    private JSONArray nameJaPath;

    /**
     * 层级
     */
    @TableField(value = "`level`")
    private Integer level;

    /**
     * 是否最低层品类（0-否，1-是）
     */
    @TableField(value = "lowest")
    private Boolean lowest;

    /**
     * 是否可以注册商品（0-否，1-是）
     */
    @TableField(value = "item_register_flg")
    private Boolean itemRegisterFlg;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
