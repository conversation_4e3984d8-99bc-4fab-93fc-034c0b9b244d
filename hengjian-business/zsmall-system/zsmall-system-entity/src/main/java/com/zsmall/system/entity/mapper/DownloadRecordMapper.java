package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.DownloadRecord;
import com.zsmall.system.entity.domain.bo.DownloadRecordBo;
import com.zsmall.system.entity.domain.vo.DownloadRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 下载记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
public interface DownloadRecordMapper extends BaseMapperPlus<DownloadRecord, DownloadRecordVo> {

    /**
     * 分页查询下载记录
     * @param downloadRecordBo
     * @return
     */
    List<DownloadRecordVo> pageDownloadRecordVoList(Page<DownloadRecordVo> page,@Param("downloadRecordBo") DownloadRecordBo downloadRecordBo,@Param("tenantId")String tenantId);

    /**
     * 查询下载记录
     * @param downloadRecordBo
     * @param tenantId
     * @return
     */
    Integer countDownloadRecordVoList(@Param("downloadRecordBo") DownloadRecordBo downloadRecordBo,@Param("tenantId")String tenantId);
}
