package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 供应商入驻基本信息附件日志表
 * @TableName tenant_sup_settle_in_basic_attachment_log
 */
@TableName(value ="tenant_sup_settle_in_basic_attachment_log")
@Data
public class TenantSupSettleInBasicAttachmentLog implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 基础信息主键
     */
    private Long basicAttachmentId;

    /**
     * 存储对象主键
     */
    private Long ossId;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件原名
     */
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    private String attachmentSuffix;

    /**
     * 附件存放路径
     */
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    private Integer attachmentSort;

    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 文件性质（证件、营业执照等）
     */
    private Integer fileNature;

    /**
     * 审核ID
     */
    private Long recordId;

    /**
     * 供应商入驻基础信息id
     */
    private Long basicId;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
