package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 交易记录附件对象 transaction_record_attachment
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("transaction_record_attachment")
@Accessors(chain = true)
public class TransactionRecordAttachment extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 存储对象主键
     */
    private Long ossId;

    /**
     * 交易记录编号
     */
    private String transactionNo;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件原名
     */
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    private String attachmentSuffix;

    /**
     * 附件存放路径
     */
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    private Long attachmentSort;

    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
