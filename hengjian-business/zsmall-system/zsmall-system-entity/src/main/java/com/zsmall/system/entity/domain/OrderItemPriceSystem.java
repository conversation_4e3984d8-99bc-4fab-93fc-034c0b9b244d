package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/4/22 17:24
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("order_item_price")
public class OrderItemPriceSystem extends SortEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 子订单表主键
     */
    private Long orderItemId;

    /**
     * 子订单编号
     */
    private String orderItemNo;

    /**
     * Sku唯一编号（ItemNo.）
     */
    private String productSkuCode;

    /**
     * 物流类型：PickUp-自提，DropShipping-代发
     */
    private LogisticsTypeEnum logisticsType;

    /**
     * 子订单商品总数量
     */
    private Integer totalQuantity;

    /**
     * 原始产品单价（供货商）
     */
    private BigDecimal originalUnitPrice;

    /**
     * 原始操作费（供货商）
     */
    private BigDecimal originalOperationFee;

    /**
     * 原始尾程派送费（供货商）
     */
    private BigDecimal originalFinalDeliveryFee;

    /**
     * 原始自提价（供货商，产品单价+操作费）
     */
    private BigDecimal originalPickUpPrice;

    /**
     * 原始代发价（供货商，产品单价+操作费+尾程派送费）
     */
    private BigDecimal originalDropShippingPrice;

    /**
     * 原始定金单价（供货商）
     */
    private BigDecimal originalDepositUnitPrice;

    /**
     * 原始尾款单价（供货商） 涉及派送逻辑
     */
    private BigDecimal originalBalanceUnitPrice;

    /**
     * 平台产品单价（平台、分销商）
     */
    private BigDecimal platformUnitPrice;

    /**
     * 平台操作费（平台、分销商）
     */
    private BigDecimal platformOperationFee;

    /**
     * 平台尾程派送费（平台、分销商）
     */
    private BigDecimal platformFinalDeliveryFee;

    /**
     * 平台自提价（平台、分销商，产品单价+操作费）
     */
    private BigDecimal platformPickUpPrice;

    /**
     * 平台代发价（平台、分销商，产品单价+操作费+尾程派送费）
     */
    private BigDecimal platformDropShippingPrice;

    /**
     * 平台定金单价（平台、分销商） 当前为0
     */
    private BigDecimal platformDepositUnitPrice;

    /**
     * 平台尾款单价（平台、分销商）  如果有会员价逻辑,这个字段就是会员价(自提/一件代发)
     */
    private BigDecimal platformBalanceUnitPrice;

    /**
     * 建议零售价
     */
    private BigDecimal msrp;

    private Long siteId;
    private String countryCode;
    private String currencySymbol;
    private String currency;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
