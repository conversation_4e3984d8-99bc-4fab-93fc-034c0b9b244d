package com.zsmall.system.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.marketplaceConfig.ApplicablePageEnum;
import com.zsmall.common.enums.marketplaceConfig.ModuleTypeEnum;
import lombok.Data;

/**
 * 商城配置表
 * <AUTHOR>
 * @date 2023/9/6
 */
@Data
@TableName(value = "marketplace_config", autoResultMap = true)
public class MarketplaceConfig extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 归属域名（COM、CN等）
     */
    @TableField(value = "domain")
    private String domain;

    /**
     * 适用页面（Homepage-首页等）
     */
    @TableField(value = "applicable_page")
    private ApplicablePageEnum applicablePage;

    /**
     * 模块类型（Banner-轮播图区域等）
     */
    @TableField(value = "module_type")
    private ModuleTypeEnum moduleType;

    /**
     * 模块正文
     */
    @TableField(value = "module_content")
    private String moduleContent;

    /**
     * 显示标志
     */
    @TableField(value = "show_flag")
    private Boolean showFlag;

    /**
     * 排序号
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
