package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.tenant.mapper.TenantMapperPlus;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.vo.salesChannel.TenantSalesChannelVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 租户渠道Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-06-07
 */
public interface TenantSalesChannelMapper extends TenantMapperPlus<TenantSalesChannel, TenantSalesChannelVo> {

    @InterceptorIgnore(tenantLine = "true")
    Object queryChannelIdAndAlias(@Param("tenantId") String tenantId, @Param("channelType") String channelType);

    List<TenantSalesChannel> selectAccountListRefactor(TenantSalesChannel tenantSalesChannel);
}
