package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 博客文章表
 *
 * @TableName blog_article
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName(value = "blog_article", autoResultMap = true)
public class BlogArticleEntity extends SortEntity implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 博客标题
     */
    @TableField(value = "blog_title")
    private String blogTitle;

    /**
     * 博客副标题
     */
    @TableField(value = "blog_sub_title")
    private String blogSubTitle;

    /**
     * 博客副标题
     */
    @TableField(value = "blog_content")
    private String blogContent;

    /**
     * 博客分类
     */
    @TableField(value = "category")
    private String category;

    /**
     * 创建者编号
     */
    @TableField(value = "creator_code")
    private String creatorCode;

    /**
     * 博客推送时间
     */
    @TableField(value = "publish_date")
    private LocalDateTime publishDate;

    /**
     * 封面展示地址
     */
    @TableField(value = "cover_show_url")
    private String coverShowUrl;

    /**
     * 封面保存路径
     */
    @TableField(value = "cover_save_path")
    private String coverSavePath;

    /**
     * 浏览次数
     */
    @TableField(value = "view_count")
    private Integer viewCount;

    /**
     * 文章语言
     */
    @TableField(value = "language")
    private String language;

    /**
     * 博客状态（Draft-草稿，Timed-定时，Published-已发送，Deleted-已删除）
     */
    @TableField(value = "blog_status")
    private String blogStatus;

    /**
     * 域名类型
     */
    @TableField(value = "domain")
    private String domain;

}
