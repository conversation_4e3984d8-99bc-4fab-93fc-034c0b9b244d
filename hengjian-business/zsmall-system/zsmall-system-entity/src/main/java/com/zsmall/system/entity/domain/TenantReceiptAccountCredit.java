package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 收款账户信用卡信息对象 tenant_receipt_account_credit
 *
 * <AUTHOR> Li
 * @date 2023-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenant_receipt_account_credit")
public class TenantReceiptAccountCredit extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 收款账户主表id
     */
    private Long receiptAccountId;

    /**
     * 开户行国别/地区（国家id）
     */
    private Long countryId;

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * 开户行名称
     */
    private String bankName;

    /**
     * SWIFT Code
     */
    private String swiftCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 币种
     */
    private String currency;

    /**
     * 开户行地址
     */
    private String bankAddress;

    /**
     * 账号持有人地址
     */
    private String holderAddress;

    /**
     * 备注
     */
    private String note;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
