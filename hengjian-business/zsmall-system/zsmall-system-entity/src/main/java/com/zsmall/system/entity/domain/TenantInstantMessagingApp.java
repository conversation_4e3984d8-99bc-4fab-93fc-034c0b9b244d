package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 用户联系方式APP对象 tenant_instant_messaging_app
 *
 * <AUTHOR> Li
 * @date 2023-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenant_instant_messaging_app")
public class TenantInstantMessagingApp extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 联系方式APP
     */
    private String messagingAppType;

    /**
     * 联系方式Id
     */
    private String messagingAppNumber;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
