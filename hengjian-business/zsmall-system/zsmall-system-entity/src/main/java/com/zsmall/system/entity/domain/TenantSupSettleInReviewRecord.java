package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.tenantSettleIn.SettleInReviewRecordEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 供应商入驻信息审核记录表
 * @TableName tenant_sup_settle_in_review_record
 */
@TableName(value ="tenant_sup_settle_in_review_record")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TenantSupSettleInReviewRecord extends NoDeptTenantEntity {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 审核人租户ID
     */
    private String reviewTenantId;

    /**
     * 审核原因
     */
    private String reviewReason;

    /**
     * 审核时间
     */
    private Date reviewTime;

    /**
     * 审核状态（Reviewing-审核中，Approved-通过，Rejected-驳回，ReviewedAgain-修改后待审核）
     */
    private SettleInReviewRecordEnum reviewState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
