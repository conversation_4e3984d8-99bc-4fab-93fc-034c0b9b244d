package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户反馈附件表
 * <AUTHOR>
 * @date 2023/9/11
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "user_feedback_attachment")
public class UserFeedbackAttachment extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 存储对象主键
     */
    @TableField(value = "oss_id")
    private Long ossId;

    /**
     * 用户反馈表主键
     */
    @TableField(value = "user_feedback_id")
    private Long userFeedbackId;

    /**
     * 附件名称
     */
    @TableField(value = "attachment_name")
    private String attachmentName;

    /**
     * 附件原名
     */
    @TableField(value = "attachment_original_name")
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    @TableField(value = "attachment_suffix")
    private String attachmentSuffix;

    /**
     * 附件存放路径
     */
    @TableField(value = "attachment_save_path")
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    @TableField(value = "attachment_show_url")
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    @TableField(value = "attachment_sort")
    private Integer attachmentSort;

    /**
     * 附件类型
     */
    @TableField(value = "attachment_type")
    private AttachmentTypeEnum attachmentType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
