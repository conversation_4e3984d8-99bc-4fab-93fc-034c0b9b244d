package com.zsmall.system.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.TransactionRecordAttachment;
import com.zsmall.system.entity.domain.vo.transaction.TransactionRecordAttachmentVo;

import java.util.List;
import java.util.Set;

/**
 * 交易记录附件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface TransactionRecordAttachmentMapper extends BaseMapperPlus<TransactionRecordAttachment, TransactionRecordAttachmentVo> {

     void insertBatch(List<TransactionRecordAttachment> transactionRecordAttachmentList);
    List<TransactionRecordAttachment> getByTransactionRecordId(Set<Long> transactionRecordIds);

    List<TransactionRecordAttachment> getAttachmentByTransactionRecordId(Set<Long> collect);

}
