package com.zsmall.system.entity.domain;


import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 钱包账单对象 bill_transaction_receipt
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bill_transaction_receipt")
public class BillTransactionReceipt extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 钱包账单编号
     */
    private String billNo;

    /**
     * 账单开始时间
     */
    private Date billStartTime;

    /**
     * 账单结束时间
     */
    private Date billEndTime;

    /**
     * 月初钱包余额
     */
    private BigDecimal walletBalanceMonthStart;

    /**
     * 月末钱包余额
     */
    private BigDecimal walletBalanceMonthEnd;

    /**
     * 当月钱包充值总金额
     */
    private BigDecimal rechargeTotalAmount;

    /**
     * 当月钱包消费总金额
     */
    private BigDecimal expenditureTotalAmount;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 币种
     */
    private String currencyCode;
    /**
     * 币种符号
     */
    private String currencySymbol;
}
