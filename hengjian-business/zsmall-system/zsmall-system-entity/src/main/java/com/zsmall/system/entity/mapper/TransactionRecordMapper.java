package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.tenant.mapper.TenantMapperPlus;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.system.entity.domain.OrdersSystemEntity;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.bo.funds.TransactionsQueryBo;
import com.zsmall.system.entity.domain.vo.funds.TransactionBusinessRelationVo;
import com.zsmall.system.entity.domain.vo.funds.TransactionsV2Vo;
import com.zsmall.system.entity.domain.vo.payment.TransactionOrderRecordVo;
import com.zsmall.system.entity.domain.vo.transaction.TransactionRecordVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 交易记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
public interface TransactionRecordMapper extends TenantMapperPlus<TransactionRecord, TransactionRecordVo> {

    TransactionOrderRecordVo getAmountByTransactionType(@Param("transactionType") TransactionTypeEnum transactionType);

    /**
     * 统计交易金额
     * @param transactionType
     * @param transactionSubType
     * @param startDate
     * @param endDate
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    BigDecimal sumTransactionAmount(@Param("transactionType") TransactionTypeEnum transactionType,
                                    @Param("transactionSubType") TransactionSubTypeEnum transactionSubType,
                                    @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 根据交易Id查询交易业务关系
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<TransactionBusinessRelationVo> selectBusinessRelations(@Param("id") Long id);
    /**
     * @description: 查询月初钱包余额
     * @author: Len
     * @date: 2024/9/29 11:47
     * @param: tenantId
     * @param: currentYearMonth
     * @return: java.math.BigDecimal
     **/
    BigDecimal selectWalletBalanceMonthStart(@Param("tenantId") String tenantId,@Param("currentYearMonth") String currentYearMonth, @Param("currencyCode")String currencyCode);
    /**
     * @description: 查询月末钱包余额
     * @author: Len
     * @date: 2024/9/29 11:47
     * @param: tenantId
     * @param: currentYearMonth
     * @return: java.math.BigDecimal
     **/
    BigDecimal selectWalletBalanceMonthEnd(@Param("tenantId") String tenantId,@Param("currentYearMonth") String currentYearMonth,@Param("currencyCode")String currencyCode);
    /**
     * @description: 根据订单子类型汇总价格
     * @author: Len
     * @date: 2024/9/27 14:22
     * @param: tenantId
     * @param: transactionSubTypeEnum
     * @param: currentYearMonth
     * @return: java.math.BigDecimal
     **/
    BigDecimal sumTotalAmountBySubType(@Param("tenantId") Object tenantId,@Param("currencyCode") String currencyCode,@Param("transactionSubTypeEnum")  List<TransactionSubTypeEnum> transactionSubTypeEnum,@Param("currentYearMonth")  String currentYearMonth);

    /**
     * 交易列表导出
     * @param bo
     * @return
     */
    List<TransactionRecord> selectListNotSysUserDel(@Param("bo") TransactionsQueryBo bo, @Param("page") Integer page, @Param("pageSize") Integer pageSize);
    /**
     * 交易列表数量
     * @param bo
     * @return
     */
    Integer selectListNotSysUserCount(@Param("bo") TransactionsQueryBo bo);

    /**
     * 交易列表查询订单
     * @param id
     * @return
     */
    List<TransactionsV2Vo> getOrderList(@Param("id") Set<Long> id);
    /**
     * 交易列表查询退款单
     * @param id
     * @return
     */
    List<TransactionsV2Vo> getOrderRefoundList(@Param("id")Set<Long> id);

    /**
     * 根据订单号查询交易记录
     * @param orderNoList
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<TransactionRecord> listByOrderNoList(@Param("orderNoList") List<String> orderNoList);
    @InterceptorIgnore(tenantLine = "true")
    List<OrdersSystemEntity> queryOrdersByOrderNo(@Param("transactionNo") Set<String> transactionNo);
    @InterceptorIgnore(tenantLine = "true")
    Page<TransactionRecord> queryPageList(Page<?> page , @Param("bo") TransactionsQueryBo bo );
}
