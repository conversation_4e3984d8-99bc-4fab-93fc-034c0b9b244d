package com.zsmall.system.entity.wrapper.impl;


import com.alibaba.fastjson.JSON;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.dto.TenantChannelDTO;
import com.zsmall.system.entity.wrapper.SendBeforeWrapper;
import org.springframework.stereotype.Component;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/8 19:32
 */
@Component
public class TenantWrapper implements SendB<PERSON>oreWrapper<TenantSalesChannel,String> {

    @Override
    public String wrapDataBeforeSending(TenantSalesChannel tenantSalesChannel) {
        TenantChannelDTO tenantChannelDTO = new TenantChannelDTO();

        return JSON.toJSONString(tenantChannelDTO);
    }
}
