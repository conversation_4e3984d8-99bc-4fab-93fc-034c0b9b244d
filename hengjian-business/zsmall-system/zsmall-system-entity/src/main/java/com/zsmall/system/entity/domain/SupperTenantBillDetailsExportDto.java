package com.zsmall.system.entity.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class SupperTenantBillDetailsExportDto {
    @ExcelProperty("账单编号")
    private String billNo;
    @ExcelProperty("分销商编号")
    private String tenantId;
    @ExcelProperty("供应商编号")
    private String supperTenantId;
    @ExcelProperty("订单号")
    private String orderNo;
    @ExcelProperty("交易类型")
    private String orderStatus;
    @ExcelProperty("下单时间")
    private Date orderTime;
    @ExcelProperty("商品编码")
    private String productSkuCode;
    @ExcelProperty("数量")
    private Integer productQuantity;
    @ExcelProperty("产品金额")
    private BigDecimal productSkuPrice;
    @ExcelProperty("操作费")
    private BigDecimal operationFee;
    @ExcelProperty("尾程派送费")
    private BigDecimal finalDeliveryFee;
    @ExcelProperty("订单金额")
    private BigDecimal orderAmount;
    @ExcelIgnore
    private BigDecimal orderTotalAmount;
    @ExcelIgnore
    private BigDecimal orderRefundTotalAmount;
}
