package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.TenantPayoneer;
import com.zsmall.system.entity.domain.vo.extraSetting.TenantPayoneerVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 支付账户管理Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-06-14
 */
public interface TenantPayoneerMapper extends BaseMapperPlus<TenantPayoneer, TenantPayoneerVo> {

    /**
     * 查询Payoneer账户
     * @param queryWrapper
     */
    TenantPayoneer selectPayoneer(@Param(Constants.WRAPPER) Wrapper<TenantPayoneer> queryWrapper);
    /**
     * 查询Payoneer账户列表
     * @param queryWrapper
     */
    List<TenantPayoneer> selectPayoneers(@Param(Constants.WRAPPER) Wrapper<TenantPayoneer> queryWrapper);
}
