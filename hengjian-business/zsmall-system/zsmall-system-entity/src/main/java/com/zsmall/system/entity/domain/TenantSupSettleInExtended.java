package com.zsmall.system.entity.domain;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zsmall.common.domain.SortEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 供应商入驻扩展信息表
 * @TableName tenant_sup_settle_in_extended
 */
@TableName(value ="tenant_sup_settle_in_extended", autoResultMap = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TenantSupSettleInExtended extends SortEntity {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 公司性质
     */
    private Integer companyNature;

    /**
     * 主营类目
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONArray mainCategories;

    /**
     * 是否有跨境电商经验
     */
    private Boolean isOnlineRetailersExperience;

    /**
     * 商品主要生产国
     */
    private Long mainProductionCountryId;

    /**
     * 是否有海外仓
     */
    private Boolean isOverseasWarehouse;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 省/州id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long stateId;

    /**
     * 省/州文本
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String stateText;

    /**
     * 城市文本
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String cityText;

    /**
     * 仓库详细地址
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String warehouseAddress;

    /**
     * 其它线上销售渠道
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONArray otherChannels;

    /**
     * 其它平台
     */
    private String otherChannelText;

    /**
     * 发货时效
     */
    private String deliverTimeLimit;

    /**
     * 近一年销售额
     */
    private BigDecimal salesPriceRecentYear;

    /**
     * 供应商入驻基础信息id
     */
    private Long basicId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
