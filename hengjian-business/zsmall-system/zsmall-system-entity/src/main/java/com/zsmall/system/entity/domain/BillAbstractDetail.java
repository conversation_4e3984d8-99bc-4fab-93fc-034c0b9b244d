package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.bill.AbstractFieldTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 账单摘要详情表
 * @TableName bill_abstract_detail
 */
@TableName(value ="bill_abstract_detail")
@Data
@EqualsAndHashCode(callSuper=false)
public class BillAbstractDetail extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 账单摘要id
     */
    private Long billAbstractId;

    /**
     * 字段类型（订单总金额、操作费总金额等可扩展字段）
     */
    private AbstractFieldTypeEnum fieldType;

    /**
     * 字段值
     */
    private BigDecimal fieldValue;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    private static final long serialVersionUID = 1L;
}
