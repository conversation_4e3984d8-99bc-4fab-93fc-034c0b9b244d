package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;


/**
 * Shopify额外属性对象 shopify_extra_properties
 *
 * <AUTHOR> Li
 * @date 2023-06-07
 */
@Data
@TableName("shopify_extra_properties")
public class ShopifyExtraProperties {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 渠道主键
     */
    private Long salesChannelId;

    /**
     * 履约服务id
     */
    private Long fulfillmentServiceId;

    /**
     * 履约服务绑定的地点id
     */
    private Long locationId;

    /**
     * 履约服务代号
     */
    private String fulfillmentServiceHandle;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
