package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 收款账户对象 tenant_receipt_account_payoneer
 *
 * <AUTHOR> Li
 * @date 2023-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenant_receipt_account_payoneer")
public class TenantReceiptAccountPayoneer extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 收款账户主表id
     */
    private Long receiptAccountId;

    /**
     * Payoneer邮箱账号
     */
    private String payoneerEmail;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
