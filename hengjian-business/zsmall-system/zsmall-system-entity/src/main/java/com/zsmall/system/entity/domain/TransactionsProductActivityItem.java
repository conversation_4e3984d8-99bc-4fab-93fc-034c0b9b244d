package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 交易记录-商品活动子表关联表 transactions_orders
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Data
@TableName("transactions_product_activity_item")
public class TransactionsProductActivityItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 交易记录主键
     */
    private Long transactionsId;

    /**
     * 商品活动子表主键
     */
    private Long productActivityItemId;

    /**
     * 创建时间
     */
    private Date createTime;


}
