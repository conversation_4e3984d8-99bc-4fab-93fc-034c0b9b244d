package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 博客分类对象 blog_category
 *
 * <AUTHOR>
 * @date 2023-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "blog_category", autoResultMap = true)
public class BlogCategory extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 父级分类主键
     */
    private Long parentId;

    /**
     * 分类代码
     */
    private String categoryCode;

    /**
     * 分类层级
     */
    private Integer categoryLevel;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类名称（英文名称）
     */
    private String categoryEnglishName;

    /**
     * 分类其他语种名称
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private String categoryOtherName;

    /**
     * 排序
     */
    private Integer categorySort;

    /**
     * 分类状态（0-停用，1-启用等）
     */
    private Integer categoryState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
