package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.system.entity.domain.OrderItemTrackingImportRecordSystem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/4/22 16:22
 */
public interface OrderItemTrackingImportRecordSystemMapper extends BaseMapper<OrderItemTrackingImportRecordSystem> {

    public List<OrderItemTrackingImportRecordSystem> queryByIds(@Param("ids")List<Long> ids);
    public List<OrderItemTrackingImportRecordSystem> queryFailedByIds(@Param("ids")List<Long> ids);
}
