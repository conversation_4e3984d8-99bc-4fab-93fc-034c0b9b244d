package com.zsmall.system.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 交易记录对象 transaction_record
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "transaction_record", autoResultMap = true)
@NoArgsConstructor
public class TransactionRecord extends NoDeptTenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 操作人员租户编号
     */
    private String operatorTenantId;

    /**
     * 交易记录编号
     */
    private String transactionNo;

    /**
     * 交易主类型（本平台与外部平台资金流动：Recharge-充值，Withdrawal-提现；本平台内部资金流动：Income-收入，Expenditure-支出）
     */
    private TransactionTypeEnum transactionType;

    /**
     * 交易子类型（记录交易具体的业务类型，如主类型为支出，子类型可以为订单支付、仓储费支付、订金支付等）
     */
    private TransactionSubTypeEnum transactionSubType;

    /**
     * 交易前余额
     */
    private BigDecimal beforeBalance;

    /**
     * 交易金额
     */
    private BigDecimal transactionAmount;

    /**
     * 交易后余额
     */
    private BigDecimal afterBalance;

    /**
     * 交易备注
     */
    private String transactionNote;

    /**
     * 交易时间
     */
    private Date transactionTime;

    /**
     * 交易状态
     */
    private TransactionStateEnum transactionState;

    /**
     * 交易失败原因
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject failureReason;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 非数据库字段，仅支付时携带前端填写的支付密码到钱包锁中校验
     */
    @TableField(exist = false)
    private String paymentPassword;
    /**
     * 店铺名称
     */
    @TableField(exist = false )
    private  String channelName;

    /**
     * 币种
     */
    private String currency;

    /**
     * 币种符号
     */
    private String currencySymbol;

    public TransactionRecord(String transactionNo) {
        this.transactionNo = transactionNo;
    }
}
