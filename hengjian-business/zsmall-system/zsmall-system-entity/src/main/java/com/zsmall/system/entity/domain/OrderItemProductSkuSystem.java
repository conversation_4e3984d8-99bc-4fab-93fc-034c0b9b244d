package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/4/22 17:24
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("order_item_product_sku")
public class OrderItemProductSkuSystem  extends NoDeptTenantEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 供货商租户编号
     */
    private String supplierTenantId;

    /**
     * 主订单编号
     */
    private String orderNo;

    /**
     * 子订单表主键
     */
    private Long orderItemId;

    /**
     * 子订单编号
     */
    private String orderItemNo;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelType;

    /**
     * 第三方销售渠道表主键
     */
    private Long channelId;

    /**
     * 第三方渠道商品变体ID（如果有则存）
     */
    private Long channelVariantId;

    /**
     * 第三方渠道仓库编号（如果有则存）
     */
    private String channelWarehouseCode;

    /**
     * 分销商商品编码
     */
    private String productCode;

    /**
     * Sku唯一编号（ItemNo.）
     */
    private String productSkuCode;

    /**
     * 最小库存单位（Sku）
     */
    private String sku;

    /**
     * 商品统一代码（UPC）
     */
    private String upc;

    /**
     * 第三方系统库存单位
     */
    private String erpSku;

    /**
     * 分销商映射Sku（第三方渠道订单才有）
     */
    private String mappingSku;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品图片存储对象主键
     */
    private Long imageOssId;

    /**
     * 商品图片存放路径
     */
    private String imageSavePath;

    /**
     * 商品图片展示地址
     */
    private String imageShowUrl;

    /**
     * 规格组成名称，示例：尺寸-大;颜色-白色
     */
    private String specComposeName;

    /**
     * 规格值名称，示例：大/白色
     */
    private String specValName;

    /**
     * 指定仓库编号（使用仓库唯一系统编号）
     */
    private String specifyWarehouse;

    /**
     * 供货商仓库唯一系统编号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String warehouseSystemCode;

    /**
     * 参与的活动类型
     */
    private ProductActivityTypeEnum activityType;

    /**
     * 参与的子活动编号
     */
    private String activityCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
