package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.TenantReceiptAccountCredit;
import com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountCreditVo;
import org.apache.ibatis.annotations.Param;

/**
 * 收款账户信用卡信息Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-07-04
 */
public interface TenantReceiptAccountCreditMapper extends BaseMapperPlus<TenantReceiptAccountCredit, TenantReceiptAccountCreditVo> {


    /**
     * 查询银行卡
     * @param queryWrapper
     * @return
     */
    TenantReceiptAccountCreditVo selectReceiptAccountCredit(@Param(Constants.WRAPPER) Wrapper<TenantReceiptAccountCredit> queryWrapper);

}
