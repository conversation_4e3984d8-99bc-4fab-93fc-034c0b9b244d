package com.zsmall.system.entity.convert;

import com.zsmall.system.entity.domain.bo.BillAdminExport;
import com.zsmall.system.entity.domain.bo.BillAdminRelationDetailDto;
import com.zsmall.system.entity.domain.bo.BillExport;
import com.zsmall.system.entity.domain.bo.BillRelationDetailDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface BillConvert {
    BillConvert INSTANCE = Mappers.getMapper(BillConvert.class);
 List<BillExport>   convertBillAdminTOBIll(List<BillAdminExport> billAdminExports);
 List<BillRelationDetailDto>   convertBillRelationTOBIll(List<BillAdminRelationDetailDto> billAdminExports);
}
