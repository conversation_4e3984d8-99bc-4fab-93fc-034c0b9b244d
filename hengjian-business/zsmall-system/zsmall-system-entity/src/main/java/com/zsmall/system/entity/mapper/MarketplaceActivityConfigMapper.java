package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.system.entity.domain.MarketplaceActivityConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商城活动表-Mapper层
 * <AUTHOR>
 * @date 2023/9/6
 */
public interface MarketplaceActivityConfigMapper extends BaseMapper<MarketplaceActivityConfig> {

    Boolean existMpActivityCode(@Param("mpActivityCode") String mpActivityCode);

    List<MarketplaceActivityConfig> getBindOtherActivity(@Param("id") Long id, @Param("bindActivityCode") String bindActivityCode);

}
