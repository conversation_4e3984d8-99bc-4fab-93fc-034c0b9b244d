package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 物流与退货模板对象 shipping_returns
 *
 * <AUTHOR> Li
 * @date 2023-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("shipping_returns")
public class ShippingReturns extends TenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 退费模板名称
     */
    private String name;

    /**
     * 自定义退费模板的原因
     */
    private String submitReason;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 状态（1-Pending-待处理，2-Approved-已批准，3-Rejected-已拒绝）
     */
    private Long status;

    /**
     * 默认状态（1-默认选中，0-非默认选中）
     */
    private Integer defaultState;

    /**
     * 拒绝的原因
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String rejectReason;

    /**
     * 员工审核时间
     */
    private Date reviewTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 用户id
     */
    private Long userId;

}
