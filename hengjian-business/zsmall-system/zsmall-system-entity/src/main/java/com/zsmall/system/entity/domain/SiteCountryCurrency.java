package com.zsmall.system.entity.domain;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 站点国家币种信息对象 site_country_currency
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@TableName("site_country_currency")
public class SiteCountryCurrency {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 站点国家
     */
    private String countryCode;

    /**
     * 站点国家名称
     */
    private String countryName;

    /**
     * 币种code
     */
    private String currencyCode;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

}
