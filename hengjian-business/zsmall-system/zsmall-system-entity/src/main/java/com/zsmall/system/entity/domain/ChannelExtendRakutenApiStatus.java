package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.channel.RakutenApiStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 渠道扩展-RakutenApi状态表
 * <AUTHOR>
 * @date 2023/11/14
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "channel_extend_rakuten_api_status")
public class ChannelExtendRakutenApiStatus extends NoDeptBaseEntity {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 渠道主键
     */
    @TableField(value = "sales_channel_id")
    private Long salesChannelId;

    /**
     * 文件柜-文件录入
     */
    @TableField(value = "cabinet_file_insert")
    private RakutenApiStatusEnum cabinetFileInsert = RakutenApiStatusEnum.Unknown;

    /**
     * 库存-录入/更新
     */
    @TableField(value = "inventory_variants_upsert")
    private RakutenApiStatusEnum inventoryVariantsUpsert = RakutenApiStatusEnum.Unknown;

    /**
     * 商品-录入/更新
     */
    @TableField(value = "items_upsert")
    private RakutenApiStatusEnum itemsUpsert = RakutenApiStatusEnum.Unknown;

    /**
     * 商品-删除
     */
    @TableField(value = "items_delete")
    private RakutenApiStatusEnum itemsDelete = RakutenApiStatusEnum.Unknown;

    /**
     * 订单-检索订单
     */
    @TableField(value = "order_search_order")
    private RakutenApiStatusEnum orderSearchOrder = RakutenApiStatusEnum.Unknown;

    /**
     * 订单-获取订单详情
     */
    @TableField(value = "order_get_order")
    private RakutenApiStatusEnum orderGetOrder = RakutenApiStatusEnum.Unknown;

    /**
     * 订单-更新订单发货状态
     */
    @TableField(value = "order_update_order_shipping")
    private RakutenApiStatusEnum orderUpdateOrderShipping = RakutenApiStatusEnum.Unknown;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
