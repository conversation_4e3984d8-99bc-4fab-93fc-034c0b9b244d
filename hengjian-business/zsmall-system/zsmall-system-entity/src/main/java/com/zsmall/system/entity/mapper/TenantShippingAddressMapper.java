package com.zsmall.system.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.TenantShippingAddress;
import com.zsmall.system.entity.domain.vo.TenantShippingAddressVo;

/**
* <AUTHOR>
* @description 针对表【tenant_shipping_address(用户收货地址表)】的数据库操作Mapper
* @createDate 2023-07-24 18:10:26
* @Entity com.zsmall.system.entity.domain.TenantShippingAddress
*/
public interface TenantShippingAddressMapper extends BaseMapperPlus<TenantShippingAddress, TenantShippingAddressVo> {

}




