package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 账单摘要详情日志表
 * @TableName bill_abstract_detail_log
 */
@TableName(value ="bill_abstract_detail_log")
@Data
@EqualsAndHashCode(callSuper=false)
public class BillAbstractDetailLog extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 账单摘要日志id
     */
    private Long billAbstractLogId;

    /**
     * 字段类型（订单总金额、操作费总金额等可扩展字段）
     */
    private String fieldType;

    /**
     * 字段值
     */
    private BigDecimal fieldValue;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    private static final long serialVersionUID = 1L;
}
