package com.zsmall.system.entity.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 分销商账单导出对象
 */
@Data
public class TenantBillHeadExportDto {
    @ExcelProperty("分销商ID")
    private String tenantId;

    @ExcelProperty("账单编号")
    private String billNo;

    @ExcelProperty("账单周期时间")
    private String billTime;

    @ExcelProperty("币种")
    private String currencyCode;

    @ExcelIgnore
    private Date billStartTime;

    @ExcelIgnore
    private Date billEndTime;

    @ExcelProperty("当月发货总金额")
    private BigDecimal totalShippedAmount;

    @ExcelProperty("当月发货总数量")
    private BigDecimal totalShippedQuantity;

    @ExcelProperty("当月退货总金额")
    private BigDecimal totalRefundAmount;

    @ExcelProperty("当月退货总数量")
    private Integer totalRefundQuantity;

    @ExcelProperty("合计")
    private BigDecimal total;

    @ExcelProperty("状态")
    private String billStatus;

}
