package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应商入驻扩展信息日志表
 * @TableName tenant_sup_settle_in_extended_log
 */
@TableName(value ="tenant_sup_settle_in_extended_log")
@Data
public class TenantSupSettleInExtendedLog implements Serializable {
    /**
     *
     */
    @TableId
    private Integer id;

    /**
     * 审核ID
     */
    private Long recordId;

    /**
     * 供应商入驻扩展信息id
     */
    private Long extendedId;

    /**
     * 公司性质
     */
    private Integer companyNature;

    /**
     * 主营类目
     */
    private String mainCategories;

    /**
     * 是否有跨境电商经验
     */
    private Boolean isOnlineRetailersExperience;

    /**
     * 商品主要生产国
     */
    private Long mainProductionCountryId;

    /**
     * 是否有海外仓
     */
    private Boolean isOverseasWarehouse;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 省/州id
     */
    private Long stateId;

    /**
     * 省/州文本
     */
    private String stateText;

    /**
     * 城市文本
     */
    private String cityText;

    /**
     * 仓库详细地址
     */
    private String warehouseAddress;

    /**
     * 其它线上销售渠道
     */
    private String otherChannels;

    /**
     * 其它平台
     */
    private String otherChannelText;

    /**
     * 发货时效
     */
    private String deliverTimeLimit;

    /**
     * 近一年销售额
     */
    private BigDecimal salesPriceRecentYear;

    /**
     * 供应商入驻基础信息id
     */
    private Long basicId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
