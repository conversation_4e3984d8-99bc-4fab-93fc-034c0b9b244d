package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.system.entity.domain.BillRelation;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
*/
public interface BillRelationMapper extends BaseMapper<BillRelation> {

  List<String> queryRelationTypeListByBillId(@Param("billId") Long billId);

  BigDecimal queryRelationTypeTotalAmount(@Param("billId") Long billId, @Param("relationType") String relationType, @Param("fieldType") String fieldType);

  List<BillRelation> queryActivityOrderRelation();

}




