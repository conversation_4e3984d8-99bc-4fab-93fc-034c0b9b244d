package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 供应商入驻基本信息日志表
 * @TableName tenant_sup_settle_in_basic_log
 */
@TableName(value ="tenant_sup_settle_in_basic_log")
@Data
public class TenantSupSettleInBasicLog implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 审核ID
     */
    private Long recordId;

    /**
     * 供应商入驻基础信息id
     */
    private Long basicId;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 省/州id
     */
    private Long stateId;

    /**
     * 省/州文本
     */
    private String stateText;

    /**
     * 城市文本
     */
    private String cityText;

    /**
     * 注册地址
     */
    private String registeredAddress;

    /**
     * 法人归属地
     */
    private String legalPersonPlace;

    /**
     * 证件类型
     */
    private Integer documentType;

    /**
     * 证件号
     */
    private String documentNumber;

    /**
     * 法定代表人姓名
     */
    private String legalPersonName;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
